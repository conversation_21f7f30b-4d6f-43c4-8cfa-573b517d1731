<?xml version="1.0" encoding="UTF-8"?>
<mxfile host="app.diagrams.net" modified="2024-01-01T00:00:00.000Z" agent="5.0" etag="xxx" version="22.1.16" type="device">
  <diagram name="MADR Task Offloading" id="flowchart">
    <mxGraphModel dx="1422" dy="794" grid="1" gridSize="10" guides="1" tooltips="1" connect="1" arrows="1" fold="1" page="1" pageScale="1" pageWidth="1169" pageHeight="827" math="1" shadow="0">
      <root>
        <mxCell id="0" />
        <mxCell id="1" parent="0" />
        
        <!-- Title -->
        <mxCell id="title" value="Multi-objective Joint Optimization of Task Offloading Based on MADR" style="text;html=1;strokeColor=none;fillColor=none;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=16;fontStyle=1;" vertex="1" parent="1">
          <mxGeometry x="284" y="20" width="600" height="30" as="geometry" />
        </mxCell>
        
        <!-- Environment Setup -->
        <mxCell id="env_box" value="" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#e1d5e7;strokeColor=#9673a6;strokeWidth=2;" vertex="1" parent="1">
          <mxGeometry x="50" y="80" width="300" height="120" as="geometry" />
        </mxCell>
        <mxCell id="env_title" value="Environment Setup" style="text;html=1;strokeColor=none;fillColor=none;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=14;fontStyle=1;" vertex="1" parent="1">
          <mxGeometry x="150" y="90" width="100" height="20" as="geometry" />
        </mxCell>
        <mxCell id="env_content" value="• Mobile Edge Computing (MEC) Network&#xa;• Multiple Users and Edge Servers&#xa;• Task Characteristics: $$T_i = \{D_i, C_i, \tau_i\}$$&#xa;• Channel Model: $$h_{i,j} = \sqrt{\frac{\beta_0}{d_{i,j}^\alpha}}$$" style="text;html=1;strokeColor=none;fillColor=none;align=left;verticalAlign=top;whiteSpace=wrap;rounded=0;fontSize=10;" vertex="1" parent="1">
          <mxGeometry x="60" y="115" width="280" height="75" as="geometry" />
        </mxCell>
        
        <!-- MADR Framework -->
        <mxCell id="madr_box" value="" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#d5e8d4;strokeColor=#82b366;strokeWidth=2;" vertex="1" parent="1">
          <mxGeometry x="400" y="80" width="320" height="120" as="geometry" />
        </mxCell>
        <mxCell id="madr_title" value="Multi-Agent Deep Reinforcement Learning (MADR)" style="text;html=1;strokeColor=none;fillColor=none;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=14;fontStyle=1;" vertex="1" parent="1">
          <mxGeometry x="460" y="90" width="200" height="20" as="geometry" />
        </mxCell>
        <mxCell id="madr_content" value="• Agent Set: $$\mathcal{A} = \{a_1, a_2, ..., a_N\}$$&#xa;• State Space: $$S_i = \{E_i, Q_i, H_i\}$$&#xa;• Action Space: $$A_i = \{local, offload_j\}$$&#xa;• Reward Function: $$R_i = -(\alpha \cdot T_i + \beta \cdot E_i + \gamma \cdot C_i)$$" style="text;html=1;strokeColor=none;fillColor=none;align=left;verticalAlign=top;whiteSpace=wrap;rounded=0;fontSize=10;" vertex="1" parent="1">
          <mxGeometry x="410" y="115" width="300" height="75" as="geometry" />
        </mxCell>
        
        <!-- Multi-objective Optimization -->
        <mxCell id="opt_box" value="" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#fff2cc;strokeColor=#d6b656;strokeWidth=2;" vertex="1" parent="1">
          <mxGeometry x="770" y="80" width="320" height="120" as="geometry" />
        </mxCell>
        <mxCell id="opt_title" value="Multi-objective Optimization" style="text;html=1;strokeColor=none;fillColor=none;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=14;fontStyle=1;" vertex="1" parent="1">
          <mxGeometry x="830" y="90" width="200" height="20" as="geometry" />
        </mxCell>
        <mxCell id="opt_content" value="• Minimize Latency: $$\min \sum_{i=1}^N T_i$$&#xa;• Minimize Energy: $$\min \sum_{i=1}^N E_i$$&#xa;• Minimize Cost: $$\min \sum_{i=1}^N C_i$$&#xa;• Pareto Optimal Solutions" style="text;html=1;strokeColor=none;fillColor=none;align=left;verticalAlign=top;whiteSpace=wrap;rounded=0;fontSize=10;" vertex="1" parent="1">
          <mxGeometry x="780" y="115" width="300" height="75" as="geometry" />
        </mxCell>
        
        <!-- Training Process -->
        <mxCell id="train_box" value="" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f8cecc;strokeColor=#b85450;strokeWidth=2;" vertex="1" parent="1">
          <mxGeometry x="50" y="250" width="500" height="200" as="geometry" />
        </mxCell>
        <mxCell id="train_title" value="MADR Training Process" style="text;html=1;strokeColor=none;fillColor=none;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=14;fontStyle=1;" vertex="1" parent="1">
          <mxGeometry x="250" y="260" width="100" height="20" as="geometry" />
        </mxCell>
        
        <!-- Training Steps -->
        <mxCell id="step1" value="1. Initialize&#xa;Neural Networks" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#dae8fc;strokeColor=#6c8ebf;" vertex="1" parent="1">
          <mxGeometry x="70" y="290" width="100" height="50" as="geometry" />
        </mxCell>
        
        <mxCell id="step2" value="2. Observe State&#xa;$$s_t^i$$" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#dae8fc;strokeColor=#6c8ebf;" vertex="1" parent="1">
          <mxGeometry x="190" y="290" width="100" height="50" as="geometry" />
        </mxCell>
        
        <mxCell id="step3" value="3. Select Action&#xa;$$a_t^i = \pi(s_t^i)$$" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#dae8fc;strokeColor=#6c8ebf;" vertex="1" parent="1">
          <mxGeometry x="310" y="290" width="100" height="50" as="geometry" />
        </mxCell>
        
        <mxCell id="step4" value="4. Execute Action&#xa;&amp; Get Reward" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#dae8fc;strokeColor=#6c8ebf;" vertex="1" parent="1">
          <mxGeometry x="430" y="290" width="100" height="50" as="geometry" />
        </mxCell>
        
        <mxCell id="step5" value="5. Update Q-Network&#xa;$$Q(s,a) \leftarrow Q(s,a) + \alpha[r + \gamma \max Q(s',a') - Q(s,a)]$$" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#dae8fc;strokeColor=#6c8ebf;" vertex="1" parent="1">
          <mxGeometry x="130" y="370" width="260" height="50" as="geometry" />
        </mxCell>
        
        <!-- Decision Making -->
        <mxCell id="decision_box" value="" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#e1d5e7;strokeColor=#9673a6;strokeWidth=2;" vertex="1" parent="1">
          <mxGeometry x="590" y="250" width="500" height="200" as="geometry" />
        </mxCell>
        <mxCell id="decision_title" value="Task Offloading Decision Making" style="text;html=1;strokeColor=none;fillColor=none;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=14;fontStyle=1;" vertex="1" parent="1">
          <mxGeometry x="740" y="260" width="200" height="20" as="geometry" />
        </mxCell>
        
        <mxCell id="local_exec" value="Local Execution&#xa;$$T_{local} = \frac{C_i}{f_i}$$&#xa;$$E_{local} = \kappa f_i^2 C_i$$" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#d5e8d4;strokeColor=#82b366;" vertex="1" parent="1">
          <mxGeometry x="610" y="300" width="140" height="80" as="geometry" />
        </mxCell>
        
        <mxCell id="offload_exec" value="Edge Offloading&#xa;$$T_{offload} = T_{trans} + T_{exec} + T_{recv}$$&#xa;$$E_{offload} = P_{trans} \cdot T_{trans}$$" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#fff2cc;strokeColor=#d6b656;" vertex="1" parent="1">
          <mxGeometry x="770" y="300" width="140" height="80" as="geometry" />
        </mxCell>
        
        <mxCell id="hybrid_exec" value="Hybrid Execution&#xa;$$\lambda_i \in [0,1]$$&#xa;$$T_{hybrid} = \max(T_{local}, T_{offload})$$" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f8cecc;strokeColor=#b85450;" vertex="1" parent="1">
          <mxGeometry x="930" y="300" width="140" height="80" as="geometry" />
        </mxCell>
        
        <!-- Performance Metrics -->
        <mxCell id="metrics_box" value="" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#d5e8d4;strokeColor=#82b366;strokeWidth=2;" vertex="1" parent="1">
          <mxGeometry x="50" y="500" width="1040" height="100" as="geometry" />
        </mxCell>
        <mxCell id="metrics_title" value="Performance Evaluation Metrics" style="text;html=1;strokeColor=none;fillColor=none;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=14;fontStyle=1;" vertex="1" parent="1">
          <mxGeometry x="520" y="510" width="200" height="20" as="geometry" />
        </mxCell>
        
        <mxCell id="metric1" value="Average Latency&#xa;$$\bar{T} = \frac{1}{N}\sum_{i=1}^N T_i$$" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#dae8fc;strokeColor=#6c8ebf;" vertex="1" parent="1">
          <mxGeometry x="80" y="540" width="150" height="50" as="geometry" />
        </mxCell>
        
        <mxCell id="metric2" value="Energy Efficiency&#xa;$$\eta_E = \frac{\sum_{i=1}^N C_i}{\sum_{i=1}^N E_i}$$" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#dae8fc;strokeColor=#6c8ebf;" vertex="1" parent="1">
          <mxGeometry x="260" y="540" width="150" height="50" as="geometry" />
        </mxCell>
        
        <mxCell id="metric3" value="System Throughput&#xa;$$\Theta = \frac{N}{T_{total}}$$" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#dae8fc;strokeColor=#6c8ebf;" vertex="1" parent="1">
          <mxGeometry x="440" y="540" width="150" height="50" as="geometry" />
        </mxCell>
        
        <mxCell id="metric4" value="Load Balancing&#xa;$$LB = 1 - \frac{\sigma}{\mu}$$" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#dae8fc;strokeColor=#6c8ebf;" vertex="1" parent="1">
          <mxGeometry x="620" y="540" width="150" height="50" as="geometry" />
        </mxCell>
        
        <mxCell id="metric5" value="Convergence Rate&#xa;$$CR = \frac{1}{T_{conv}}$$" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#dae8fc;strokeColor=#6c8ebf;" vertex="1" parent="1">
          <mxGeometry x="800" y="540" width="150" height="50" as="geometry" />
        </mxCell>
        
        <!-- Arrows -->
        <mxCell id="arrow1" value="" style="endArrow=classic;html=1;rounded=0;exitX=1;exitY=0.5;entryX=0;entryY=0.5;" edge="1" parent="1" source="env_box" target="madr_box">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="360" y="140" as="sourcePoint" />
            <mxPoint x="410" y="90" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        
        <mxCell id="arrow2" value="" style="endArrow=classic;html=1;rounded=0;exitX=1;exitY=0.5;entryX=0;entryY=0.5;" edge="1" parent="1" source="madr_box" target="opt_box">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="730" y="140" as="sourcePoint" />
            <mxPoint x="780" y="90" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        
        <mxCell id="arrow3" value="" style="endArrow=classic;html=1;rounded=0;exitX=0.5;exitY=1;entryX=0.5;entryY=0;" edge="1" parent="1" source="env_box" target="train_box">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="200" y="210" as="sourcePoint" />
            <mxPoint x="250" y="160" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        
        <mxCell id="arrow4" value="" style="endArrow=classic;html=1;rounded=0;exitX=0.5;exitY=1;entryX=0.5;entryY=0;" edge="1" parent="1" source="opt_box" target="decision_box">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="930" y="210" as="sourcePoint" />
            <mxPoint x="980" y="160" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        
        <!-- Training flow arrows -->
        <mxCell id="train_arrow1" value="" style="endArrow=classic;html=1;rounded=0;exitX=1;exitY=0.5;entryX=0;entryY=0.5;" edge="1" parent="1" source="step1" target="step2">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="180" y="315" as="sourcePoint" />
            <mxPoint x="230" y="265" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        
        <mxCell id="train_arrow2" value="" style="endArrow=classic;html=1;rounded=0;exitX=1;exitY=0.5;entryX=0;entryY=0.5;" edge="1" parent="1" source="step2" target="step3">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="300" y="315" as="sourcePoint" />
            <mxPoint x="350" y="265" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        
        <mxCell id="train_arrow3" value="" style="endArrow=classic;html=1;rounded=0;exitX=1;exitY=0.5;entryX=0;entryY=0.5;" edge="1" parent="1" source="step3" target="step4">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="420" y="315" as="sourcePoint" />
            <mxPoint x="470" y="265" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        
        <mxCell id="train_arrow4" value="" style="endArrow=classic;html=1;rounded=0;exitX=0.5;exitY=1;entryX=0.5;entryY=0;" edge="1" parent="1" source="step4" target="step5">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="480" y="350" as="sourcePoint" />
            <mxPoint x="530" y="300" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        
        <!-- Feedback arrow -->
        <mxCell id="feedback_arrow" value="" style="endArrow=classic;html=1;rounded=0;exitX=0;exitY=0.5;entryX=0.5;entryY=1;curved=1;" edge="1" parent="1" source="step5" target="step1">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="120" y="395" as="sourcePoint" />
            <mxPoint x="170" y="345" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        
      </root>
    </mxGraphModel>
  </diagram>
</mxfile>
